import React from 'react';
import { screen, fireEvent } from '@testing-library/react';
import { SignInComponent } from './SignInComponent';
import {
  renderInTestApp,
  TestApiProvider,
} from '@backstage/test-utils';

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('SignInComponent', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('should render the sign-in page with title', async () => {
    await renderInTestApp(
      <TestApiProvider apis={[]}>
        <SignInComponent />
      </TestApiProvider>
    );

    expect(screen.getByText('Sign In to Choreo')).toBeInTheDocument();
    expect(screen.getByText('Choose your preferred sign-in method')).toBeInTheDocument();
  });

  it('should render all sign-in options', async () => {
    await renderInTestApp(
      <TestApiProvider apis={[]}>
        <SignInComponent />
      </TestApiProvider>
    );

    expect(screen.getByText('Continue with Google')).toBeInTheDocument();
    expect(screen.getByText('Continue with GitHub')).toBeInTheDocument();
    expect(screen.getByText('Continue with Microsoft')).toBeInTheDocument();
    expect(screen.getByText('Sign in with Enterprise ID')).toBeInTheDocument();
    expect(screen.getByText('Sign in with Email')).toBeInTheDocument();
  });

  it('should navigate to home page when a sign-in option is clicked', async () => {
    await renderInTestApp(
      <TestApiProvider apis={[]}>
        <SignInComponent />
      </TestApiProvider>
    );

    const googleButton = screen.getByTestId('signin-google');
    fireEvent.click(googleButton);

    expect(mockNavigate).toHaveBeenCalledWith('/choreo-home');
  });

  it('should have proper accessibility attributes', async () => {
    await renderInTestApp(
      <TestApiProvider apis={[]}>
        <SignInComponent />
      </TestApiProvider>
    );

    const googleButton = screen.getByLabelText('Sign in with google');
    expect(googleButton).toBeInTheDocument();
    
    const githubButton = screen.getByLabelText('Sign in with github');
    expect(githubButton).toBeInTheDocument();
  });

  it('should render buttons with correct test ids', async () => {
    await renderInTestApp(
      <TestApiProvider apis={[]}>
        <SignInComponent />
      </TestApiProvider>
    );

    expect(screen.getByTestId('signin-google')).toBeInTheDocument();
    expect(screen.getByTestId('signin-github')).toBeInTheDocument();
    expect(screen.getByTestId('signin-microsoft')).toBeInTheDocument();
    expect(screen.getByTestId('signin-enterprise')).toBeInTheDocument();
    expect(screen.getByTestId('signin-email')).toBeInTheDocument();
  });
});
