import React from 'react';
import {
  Typography,
  Grid,
  makeStyles,
  Theme,
} from '@material-ui/core';
import {
  InfoCard,
  Page,
  Content,
  ContentHeader,
  Header,
} from '@backstage/core-components';
import { useNavigate } from 'react-router-dom';

const useStyles = makeStyles((theme: Theme) => ({
  root: {
    backgroundColor: theme.palette.background.default,
  },
  welcomeCard: {
    padding: theme.spacing(3),
  },
  welcomeText: {
    marginBottom: theme.spacing(2),
  },
  placeholderText: {
    color: theme.palette.text.secondary,
    fontStyle: 'italic',
  },
}));

export interface HomeComponentProps {
  /** Optional custom title for the page */
  title?: string;
  /** Optional custom subtitle for the page */
  subtitle?: string;
}

export const HomeComponent: React.FC<HomeComponentProps> = ({
  title = 'Welcome to Choreo',
  subtitle = 'Your development platform dashboard',
}) => {
  const classes = useStyles();
  const navigate = useNavigate();

  const handleBackToSignIn = () => {
    navigate('/choreo-signin');
  };

  return (
    <Page themeId="home" className={classes.root}>
      <Header title={title} subtitle={subtitle} />
      <Content>
        <ContentHeader title="Dashboard">
          <Typography variant="body1" color="textSecondary">
            Welcome to your Choreo dashboard. This is where you'll manage your projects and services.
          </Typography>
        </ContentHeader>
        <Grid container spacing={3} direction="column">
          <Grid item>
            <InfoCard title="Getting Started" className={classes.welcomeCard}>
              <Typography variant="body1" className={classes.welcomeText}>
                Welcome to Choreo! You have successfully signed in to your account.
              </Typography>
              <Typography variant="body2" className={classes.placeholderText}>
                This is a placeholder home page. Future features will include:
              </Typography>
              <ul>
                <li>
                  <Typography variant="body2" className={classes.placeholderText}>
                    Project management dashboard
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2" className={classes.placeholderText}>
                    Service deployment status
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2" className={classes.placeholderText}>
                    Analytics and monitoring
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2" className={classes.placeholderText}>
                    Team collaboration tools
                  </Typography>
                </li>
              </ul>
            </InfoCard>
          </Grid>
          <Grid item>
            <InfoCard title="Quick Actions">
              <Typography variant="body2" className={classes.placeholderText}>
                Quick action buttons and shortcuts will be available here.
              </Typography>
            </InfoCard>
          </Grid>
        </Grid>
      </Content>
    </Page>
  );
};
